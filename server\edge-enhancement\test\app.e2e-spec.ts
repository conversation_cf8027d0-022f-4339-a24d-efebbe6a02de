import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('EdgeEnhancementController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('健康检查', () => {
    it('/api/v1/edge-enhancement/health (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'healthy');
          expect(res.body).toHaveProperty('timestamp');
        });
    });

    it('/api/v1/edge-enhancement/status (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/status')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('timestamp');
          expect(res.body).toHaveProperty('services');
          expect(res.body).toHaveProperty('version');
          expect(res.body).toHaveProperty('uptime');
        });
    });
  });

  describe('智能调度服务', () => {
    it('/api/v1/edge-enhancement/scheduler/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/scheduler/statistics')
        .expect(200);
    });

    it('/api/v1/edge-enhancement/scheduler/predict/test-node (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/scheduler/predict/test-node')
        .expect(200);
    });
  });

  describe('预测性缓存服务', () => {
    it('/api/v1/edge-enhancement/cache/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/cache/statistics')
        .expect(200);
    });

    it('/api/v1/edge-enhancement/cache/test-key (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/cache/test-key')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('key', 'test-key');
          expect(res.body).toHaveProperty('found');
        });
    });
  });

  describe('自适应网络服务', () => {
    it('/api/v1/edge-enhancement/network/statistics (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/network/statistics')
        .expect(200);
    });
  });

  describe('配置管理', () => {
    it('/api/v1/edge-enhancement/config (GET)', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge-enhancement/config')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('scheduler');
          expect(res.body).toHaveProperty('cache');
          expect(res.body).toHaveProperty('network');
        });
    });
  });
});
