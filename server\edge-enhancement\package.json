{"name": "edge-enhancement-service", "version": "1.0.0", "description": "边缘计算增强服务 - 提供智能调度、预测性缓存和自适应网络传输功能", "author": "DL Engine Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:build": "docker build -t edge-enhancement-service .", "docker:run": "docker run -p 3040:3040 edge-enhancement-service", "docker:compose": "docker-compose up -d", "docker:compose:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:down": "docker-compose down", "migration:generate": "typeorm migration:generate", "migration:run": "typeorm migration:run", "migration:revert": "typeorm migration:revert"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/schedule": "^3.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/throttler": "^4.0.0", "typeorm": "^0.3.17", "pg": "^8.11.0", "redis": "^4.6.0", "ioredis": "^5.3.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcrypt": "^5.1.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "compression": "^1.7.4", "helmet": "^7.0.0", "express-rate-limit": "^6.7.0", "winston": "^3.9.0", "winston-daily-rotate-file": "^4.7.1", "prom-client": "^14.2.0", "swagger-ui-express": "^4.6.3", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "node-cron": "^3.0.2", "zlib": "^1.0.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcrypt": "^5.0.0", "@types/uuid": "^9.0.2", "@types/lodash": "^4.14.195", "@types/compression": "^1.7.2", "@types/multer": "^1.4.7", "@types/node-cron": "^3.0.7", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["edge-computing", "intelligent-scheduling", "predictive-cache", "adaptive-network", "<PERSON><PERSON><PERSON>", "microservice", "dl-engine"]}